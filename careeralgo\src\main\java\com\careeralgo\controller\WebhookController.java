package com.careeralgo.controller;

import com.careeralgo.service.ClerkWebhookService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for handling external webhooks
 */
@RestController
@RequestMapping("/webhooks")
public class WebhookController {

    private static final Logger logger = LoggerFactory.getLogger(WebhookController.class);

    @Autowired
    private ClerkWebhookService clerkWebhookService;

    /**
     * Handle Clerk user lifecycle webhooks
     */
    @PostMapping("/clerk/user")
    public ResponseEntity<String> handleClerkUserWebhook(
            @RequestBody String payload,
            @RequestHeader(value = "svix-signature", required = false) String signature,
            @RequestHeader(value = "svix-timestamp", required = false) String timestamp,
            @RequestHeader(value = "svix-id", required = false) String webhookId) {

        try {
            logger.info("Received Clerk webhook with ID: {}", webhookId);

            // Validate webhook signature
            if (signature == null || timestamp == null) {
                logger.warn("Missing required webhook headers");
                return ResponseEntity.badRequest().body("Missing required headers");
            }

            // if (!clerkWebhookService.validateSignature(payload, signature, timestamp)) {
            //     logger.warn("Invalid webhook signature");
            //     return ResponseEntity.status(401).body("Invalid signature");
            // }

            // TODO: Check for duplicate webhooks using webhookId
            // This would require a cache or database to store processed webhook IDs

            // Process the webhook event
            clerkWebhookService.processEvent(payload);

            logger.info("Successfully processed Clerk webhook: {}", webhookId);
            return ResponseEntity.ok("Webhook processed successfully");

        } catch (Exception e) {
            logger.error("Error processing Clerk webhook", e);
            return ResponseEntity.status(500).body("Internal server error");
        }
    }

    /**
     * Handle Clerk session webhooks (optional)
     */
    @PostMapping("/clerk/session")
    public ResponseEntity<String> handleClerkSessionWebhook(
            @RequestBody String payload,
            @RequestHeader(value = "svix-signature", required = false) String signature,
            @RequestHeader(value = "svix-timestamp", required = false) String timestamp,
            @RequestHeader(value = "svix-id", required = false) String webhookId) {

        try {
            logger.info("Received Clerk session webhook with ID: {}", webhookId);

            // Validate webhook signature
            if (signature == null || timestamp == null) {
                return ResponseEntity.badRequest().body("Missing required headers");
            }

            if (!clerkWebhookService.validateSignature(payload, signature, timestamp)) {
                return ResponseEntity.status(401).body("Invalid signature");
            }

            // TODO: Implement session event handling if needed
            // For now, just acknowledge receipt
            logger.info("Acknowledged Clerk session webhook: {}", webhookId);
            return ResponseEntity.ok("Session webhook acknowledged");

        } catch (Exception e) {
            logger.error("Error processing Clerk session webhook", e);
            return ResponseEntity.status(500).body("Internal server error");
        }
    }

    /**
     * Health check endpoint for webhooks
     */
    @GetMapping("/health")
    public ResponseEntity<String> webhookHealth() {
        return ResponseEntity.ok("Webhook endpoints are healthy");
    }
}
