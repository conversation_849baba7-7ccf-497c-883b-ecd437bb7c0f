package com.careeralgo.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;



/**
 * Custom JWT decoder for Clerk tokens using proper RSA signature verification
 */
public class ClerkJwtDecoder implements JwtDecoder {

    private static final Logger logger = LoggerFactory.getLogger(ClerkJwtDecoder.class);

    private final JwtDecoder delegate;
    private final String issuerUri;

    public ClerkJwtDecoder(String issuerUri) {
        this.issuerUri = issuerUri;
        this.delegate = createJwtDecoder();
    }

    private JwtDecoder createJwtDecoder() {
        try {
            // Use Spring Security's NimbusJwtDecoder with JWKS endpoint
            String jwksUri = issuerUri + "/.well-known/jwks.json";
            logger.info("Configuring JWT decoder with JWKS URI: {}", jwksUri);

            NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder
                    .withJwkSetUri(jwksUri)
                    .build();

            // Configure JWT validation
            jwtDecoder.setJwtValidator(new ClerkJwtValidator(issuerUri));

            return jwtDecoder;
        } catch (Exception e) {
            logger.error("Failed to create JWT decoder for issuer: {}", issuerUri, e);
            throw new IllegalStateException("Could not create JWT decoder", e);
        }
    }

    @Override
    public Jwt decode(String token) throws JwtException {
        try {
            logger.debug("Decoding JWT token");
            Jwt jwt = delegate.decode(token);
            logger.debug("Successfully decoded JWT token for subject: {}", jwt.getSubject());
            return jwt;
        } catch (Exception e) {
            logger.error("Failed to decode JWT token: {}", e.getMessage());
            throw new JwtException("Failed to decode JWT token", e);
        }
    }
}
