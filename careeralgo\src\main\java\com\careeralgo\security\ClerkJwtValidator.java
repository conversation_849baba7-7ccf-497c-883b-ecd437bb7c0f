package com.careeralgo.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtTimestampValidator;
import org.springframework.security.oauth2.jwt.JwtIssuerValidator;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom JWT validator for Clerk tokens
 */
public class ClerkJwtValidator implements OAuth2TokenValidator<Jwt> {

    private static final Logger logger = LoggerFactory.getLogger(ClerkJwtValidator.class);
    
    private final List<OAuth2TokenValidator<Jwt>> validators;

    public ClerkJwtValidator(String issuerUri) {
        this.validators = new ArrayList<>();
        
        // Add standard validators
        this.validators.add(new JwtTimestampValidator(Duration.ofSeconds(60))); // Allow 60 seconds clock skew
        this.validators.add(new JwtIssuerValidator(issuerUri));
        
        logger.info("Configured JWT validator for issuer: {}", issuerUri);
    }

    @Override
    public OAuth2TokenValidatorResult validate(Jwt jwt) {
        List<OAuth2Error> errors = new ArrayList<>();
        
        // Run all validators
        for (OAuth2TokenValidator<Jwt> validator : validators) {
            OAuth2TokenValidatorResult result = validator.validate(jwt);
            if (result.hasErrors()) {
                errors.addAll(result.getErrors());
            }
        }
        
        // Additional Clerk-specific validations
        if (!validateClerkSpecificClaims(jwt)) {
            errors.add(new OAuth2Error("invalid_clerk_claims", "Invalid Clerk-specific claims", null));
        }
        
        if (errors.isEmpty()) {
            logger.debug("JWT validation successful for subject: {}", jwt.getSubject());
            return OAuth2TokenValidatorResult.success();
        } else {
            logger.warn("JWT validation failed with {} errors", errors.size());
            return OAuth2TokenValidatorResult.failure(errors);
        }
    }
    
    private boolean validateClerkSpecificClaims(Jwt jwt) {
        try {
            // Validate that required Clerk claims are present
            String subject = jwt.getSubject();
            if (subject == null || subject.isEmpty()) {
                logger.warn("JWT missing subject claim");
                return false;
            }
            
            // Validate that the subject follows Clerk's user ID format
            if (!subject.startsWith("user_")) {
                logger.warn("JWT subject does not follow Clerk user ID format: {}", subject);
                return false;
            }
            
            // Additional Clerk-specific validations can be added here
            return true;
        } catch (Exception e) {
            logger.error("Error validating Clerk-specific claims", e);
            return false;
        }
    }
}
